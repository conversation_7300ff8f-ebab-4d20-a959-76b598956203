name: Build and Publish Docker Images Prod

on:
  push:
    branches: [main, develop]

  workflow_dispatch:

concurrency:
  group: ${{ github.ref }}-${{ github.workflow }}
  cancel-in-progress: true

permissions:
  contents: read
  packages: write

jobs:
  ever-works-api:
    runs-on: buildjet-4vcpu-ubuntu-2204
    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: Set up QEMU
        uses: docker/setup-qemu-action@v3

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3
        with:
          driver: docker

      - name: Build
        uses: docker/build-push-action@v5
        with:
          context: .
          file: .deploy/docker/api/Dockerfile
          load: true
          platforms: linux/amd64
          tags: |
            ghcr.io/ever-works/ever-works-api:latest
            everco/ever-works-api:latest
            registry.digitalocean.com/ever/ever-works-api:latest
          cache-from: type=registry,ref=ghcr.io/ever-works/ever-works-api:latest
          cache-to: type=inline
          build-args: |
            NODE_ENV=production

      - name: Docker images list
        run: |
          sudo docker image list

      - name: Login to DockerHub
        uses: docker/login-action@v3
        continue-on-error: true
        with:
          username: ${{ secrets.DOCKERHUB_USERNAME }}
          password: ${{ secrets.DOCKERHUB_TOKEN }}

      - name: Push to Docker Hub Registry
        continue-on-error: true
        run: |
          docker push everco/ever-works-api:latest

      - name: Install doctl

        uses: digitalocean/action-doctl@v2
        continue-on-error: true
        with:
          token: ${{ secrets.DIGITALOCEAN_ACCESS_TOKEN }}

      - name: Log in to DigitalOcean Container Registry with short-lived credentials
        continue-on-error: true
        run: doctl registry login --expiry-seconds 3600

      - name: Push to DigitalOcean Registry
        continue-on-error: true
        run: |
          docker push registry.digitalocean.com/ever/ever-works-api:latest

      - name: Login to GitHub Container Registry
        uses: docker/login-action@v3
        with:
          registry: ghcr.io
          username: ${{ github.repository_owner }}
          password: ${{ secrets.GITHUB_TOKEN }}

      - name: Push to Github Registry
        run: |
          docker push ghcr.io/ever-works/ever-works-api:latest

      - name: Login to CW Container Registry
        uses: docker/login-action@v3
        continue-on-error: true
        with:
          registry: ${{ secrets.CW_DOCKER_REGISTRY }}
          username: ${{ secrets.CW_DOCKER_USER }}
          password: ${{ secrets.CW_DOCKER_USER_PASSWORD }}

    #  - name: Push to CW Registry
    #    run: |
    #      docker push ${{ secrets.CW_DOCKER_REGISTRY }}/ever-co/ever-works-api:latest

  ever-works-web:
    runs-on: buildjet-4vcpu-ubuntu-2204
    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: Set up QEMU
        uses: docker/setup-qemu-action@v3

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3

      - name: Build
        uses: docker/build-push-action@v5
        with:
          context: .
          file: .deploy/docker/web/Dockerfile
          load: true
          platforms: linux/amd64
          tags: |
            ghcr.io/ever-works/ever-works-web:latest
            everco/ever-works-web:latest
            registry.digitalocean.com/ever/ever-works-web:latest
          cache-from: type=registry,ref=ghcr.io/ever-works/ever-works-web:latest
          cache-to: type=inline
          build-args: |
            NODE_ENV=production

      - name: Docker images list
        run: |
          sudo docker image list

      - name: Login to DockerHub
        uses: docker/login-action@v3
        continue-on-error: true
        with:
          username: ${{ secrets.DOCKERHUB_USERNAME }}
          password: ${{ secrets.DOCKERHUB_TOKEN }}

      - name: Push to Docker Hub Registry
        continue-on-error: true
        run: |
          docker push everco/ever-works-web:latest

      - name: Install doctl
        uses: digitalocean/action-doctl@v2
        continue-on-error: true
        with:
          token: ${{ secrets.DIGITALOCEAN_ACCESS_TOKEN }}

      - name: Log in to DigitalOcean Container Registry with short-lived credentials
        continue-on-error: true
        run: doctl registry login --expiry-seconds 3600

      - name: Push to DigitalOcean Registry
        continue-on-error: true
        run: |
          docker push registry.digitalocean.com/ever/ever-works-web:latest

      - name: Login to GitHub Container Registry
        uses: docker/login-action@v3
        with:
          registry: ghcr.io
          username: ${{ github.repository_owner }}
          password: ${{ secrets.GITHUB_TOKEN }}

      - name: Push to Github Registry
        run: |
          docker push ghcr.io/ever-works/ever-works-web:latest

      - name: Login to CW Container Registry
        uses: docker/login-action@v3
        continue-on-error: true
        with:
          registry: ${{ secrets.CW_DOCKER_REGISTRY }}
          username: ${{ secrets.CW_DOCKER_USER }}
          password: ${{ secrets.CW_DOCKER_USER_PASSWORD }}

    #  - name: Push to CW Registry
    #    run: |
    #      docker push ${{ secrets.CW_DOCKER_REGISTRY }}/ever-co/ever-works-web:latest
