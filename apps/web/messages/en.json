{"auth": {"error": {"title": "Authentication Error", "subtitle": "We encountered an issue with your authentication", "errorOccurred": "An error occurred", "somethingWentWrong": "Something went wrong", "generic": "An unexpected error occurred. Please try again later.", "invalidCredentials": "The email or password you entered is incorrect.", "emailNotVerified": "Please verify your email address before signing in.", "accountLocked": "Your account has been locked for security reasons.", "sessionExpired": "Your session has expired. Please sign in again.", "networkError": "Unable to connect. Please check your internet connection.", "oauth": {"missingCode": "Authorization code is missing. Please try signing in again.", "invalidState": "Invalid authorization state. Please try signing in again.", "unsupportedProvider": "This authentication provider is not supported.", "callbackFailed": "Failed to complete authentication. Please try again."}, "resetPassword": {"missingToken": "Reset password link is invalid. Missing verification token.", "invalidToken": "Reset password link is invalid or has been tampered with.", "expiredToken": "Reset password link has expired. Please request a new one.", "failed": "Failed to reset password. Please try again or request a new reset link."}, "verifyEmail": {"missingToken": "Email verification link is invalid. Missing verification token.", "invalidToken": "Email verification link is invalid or has already been used.", "expiredToken": "Email verification link has expired. Please request a new one.", "failed": "Failed to verify email. Please try again or request a new verification link."}, "authorize": {"invalidRedirectUrl": "The redirect URL is invalid or not allowed. Please contact support if you believe this is an error."}, "actions": {"backToLogin": "Back to Login", "tryRegister": "Try Registering", "resendVerification": "Resend Verification Email", "contactSupport": "Contact Support", "requestNewReset": "Request New Reset Link"}, "helpText": "If you continue to experience issues, please check our help resources below.", "links": {"help": "Help Center", "systemStatus": "System Status"}}, "login": {"title": "Welcome back", "subtitle": "Sign in to your account to continue", "form": {"email": {"label": "Email address", "placeholder": "<EMAIL>"}, "password": {"label": "Password", "placeholder": "Enter your password"}, "forgotPassword": "Forgot password?", "rememberMe": "Remember me for 30 days", "submit": "Sign in", "submitting": "Signing in..."}, "socialLogin": {"divider": "Or continue with", "google": "Google", "github": "GitHub"}, "signUp": {"text": "Don't have an account?", "link": "Sign up"}, "errors": {"invalidCredentials": "Invalid email or password"}, "passwordReset": {"success": "Password reset successful!", "canLogin": "You can now sign in with your new password."}}, "forgotPassword": {"title": "Forgot your password?", "subtitle": "No worries, we'll send you reset instructions", "successTitle": "Check your email", "successSubtitle": "We've sent you password reset instructions", "form": {"email": {"label": "Email address", "placeholder": "Enter your email"}, "submit": "Send Reset Link", "submitting": "Sending..."}, "success": {"title": "<PERSON>ail sent successfully", "message": "We've sent password reset instructions to {email}", "checkSpam": "If you don't see the email, check your spam folder."}, "errors": {"failed": "Failed to send reset email. Please try again."}, "backToLogin": "Back to login", "noAccount": {"text": "Don't have an account?", "link": "Sign up"}}, "resetPassword": {"title": "Reset your password", "subtitle": "Enter your new password below", "successTitle": "Password reset successful", "successSubtitle": "Your password has been updated", "form": {"password": {"label": "New password", "placeholder": "Enter your new password", "helperText": "Must be at least 6 characters with lowercase and number/special character", "errors": {"minLength": "Password must be at least 6 characters", "lowercase": "Password must contain at least one lowercase letter", "numberOrSpecial": "Password must contain at least one number or special character", "cannotStartWith": "Password cannot start with a dot or newline"}}, "confirmPassword": {"label": "Confirm new password", "placeholder": "Confirm your new password", "errors": {"noMatch": "Passwords do not match"}}, "submit": "Reset Password", "submitting": "Resetting..."}, "success": {"title": "Password reset successful", "message": "Your password has been successfully updated. You can now login with your new password.", "loginButton": "Go to Login"}, "errors": {"failed": "Failed to reset password. Please try again or request a new reset link.", "noToken": "Invalid reset link", "invalidLink": "Invalid Reset Link", "missingTokenMessage": "The password reset link is invalid or incomplete. Please request a new password reset.", "requestNewLink": "Request New Reset Link"}, "backToLogin": "Back to login"}, "register": {"title": "Create your account", "subtitle": "Start building amazing directories today", "form": {"name": {"label": "Full name", "placeholder": "<PERSON>"}, "email": {"label": "Email address", "placeholder": "<EMAIL>"}, "password": {"label": "Password", "placeholder": "Create a strong password", "hint": "Must be at least 8 characters"}, "confirmPassword": {"label": "Confirm password", "placeholder": "Confirm your password"}, "terms": {"text": "I agree to the", "termsLink": "Terms of Service", "and": "and", "privacyLink": "Privacy Policy"}, "submit": "Create account", "submitting": "Creating account..."}, "socialSignUp": {"divider": "Or sign up with", "google": "Google", "github": "GitHub"}, "signIn": {"text": "Already have an account?", "link": "Sign in"}, "errors": {"passwordsDoNotMatch": "Passwords do not match", "passwordTooShort": "Password must be at least 8 characters", "generic": "An error occurred. Please try again."}}}, "dashboard": {"title": "Dashboard", "toasts": {"newUser": {"title": "Welcome to Ever Works! 🎉", "description": "Your account has been created successfully. Please check your email to verify your account."}, "emailVerification": {"title": "Verify your email", "description": "We've sent a verification link to your email address. Please check your inbox and spam folder.", "action": "Resend email"}, "verified": {"title": "Email verified successfully! ✅", "description": "Your email address has been verified. You now have full access to all features."}, "oauthConnected": {"title": "Account connected to GitHub", "description": "Your account has been successfully connected to GitHub."}}, "header": {"welcome": "Welcome back, {username}!", "subtitle": "Manage your AI-powered directories and track their performance", "notifications": {"title": "Notifications", "empty": "No new notifications"}}, "directories": {"title": "Directories", "subtitle": "Manage and organize your AI-powered directories", "recent": "Recent Directories", "viewAll": "View all ({count})", "search": "Search directories...", "create": "Create Directory", "showing": "Showing {current} of {total} directories", "pagination": {"previous": "Previous", "next": "Next"}, "empty": {"title": "No directories yet", "description": "Create your first AI-powered directory to start organizing and showcasing your content.", "action": "Create Your First Directory", "notFound": {"title": "No directories found", "withSearch": "Try adjusting your search terms", "withoutSearch": "Create your first AI-powered directory to get started"}}}, "settings": {"title": "Settings", "subtitle": "Manage your account settings and preferences", "tabs": {"profile": "Profile", "security": "Security", "apiTokens": "API & Tokens", "oauth": "Connected Accounts", "notifications": "Notifications", "dangerZone": "Danger Zone"}, "profile": {"title": "Profile Settings", "subtitle": "Update your profile information and avatar", "fields": {"username": "Username", "email": "Email", "emailHelperText": "Email cannot be changed"}, "placeholders": {"username": "Enter your username"}, "actions": {"save": "Save Changes"}, "messages": {"success": "Profile updated successfully", "usernameRequired": "Username is required", "error": "Failed to update profile", "unexpectedError": "An unexpected error occurred"}}, "security": {"title": "Security Settings", "subtitle": "Manage your account security and authentication", "changePassword": {"title": "Change Password", "currentPassword": "Current Password", "newPassword": "New Password", "confirmPassword": "Confirm New Password", "showPasswords": "Show passwords", "placeholders": {"current": "Enter current password", "new": "Enter new password (min 8 characters)", "confirm": "Confirm new password"}, "actions": {"update": "Update Password"}, "messages": {"success": "Password updated successfully", "fillAllFields": "Please fill in all password fields", "minLength": "New password must be at least 8 characters", "mismatch": "New passwords do not match", "sameAsCurrent": "New password must be different from current password", "error": "Failed to update password", "unexpectedError": "An unexpected error occurred"}}, "twoFactor": {"title": "Two-Factor Authentication", "subtitle": "Add an extra layer of security to your account", "action": "Coming Soon"}, "sessions": {"title": "Active Sessions", "subtitle": "Manage your active sessions across devices", "action": "View Sessions (Coming Soon)"}}, "oauth": {"title": "Connected Accounts", "subtitle": "Connect your external accounts for enhanced functionality", "github": {"name": "GitHub", "connected": "Create and manage directories with GitHub repositories", "disconnected": "Connect to create directories from repositories", "scopes": "<PERSON><PERSON><PERSON>", "actions": {"connect": "Connect", "disconnect": "Disconnect", "confirmDisconnect": "Are you sure you want to disconnect your GitHub account?"}, "messages": {"disconnected": "GitHub account disconnected", "connectError": "Failed to connect GitHub", "disconnectError": "Failed to disconnect GitHub"}}}}, "stats": {"totalDirectories": "Total Directories", "totalItems": "Total Items", "activeWebsites": "Active Websites", "fromLastMonth": "from last month"}, "directoryCard": {"status": {"active": "Active"}, "noDescription": "No description provided", "items": "{count} items", "views": "{count} views", "updated": "Updated {date}", "viewAction": "View →"}, "apiTokens": {"title": "API & Tokens", "subtitle": "Manage your API keys and third-party integrations", "vercel": {"title": "Vercel Integration", "subtitle": "Connect your Vercel account to deploy directories as websites", "connected": "Connected", "notConnected": "Not Connected", "connectedMessage": "Your Vercel account is connected. You can now deploy directories to Vercel.", "disconnect": "Disconnect Vercel", "disconnecting": "Removing...", "apiTokenLabel": "Vercel API Token", "placeholder": "vc_...", "showToken": "Show", "hideToken": "<PERSON>de", "getTokenHelp": "You can get your API token from", "vercelDashboard": "Vercel Dashboard", "save": "Save Token", "saving": "Saving...", "messages": {"tokenRequired": "Please enter a valid Vercel token", "saveSuccess": "Vercel token saved successfully", "saveFailed": "Failed to save <PERSON><PERSON><PERSON> token", "removeSuccess": "Vercel token removed successfully", "removeFailed": "Failed to remove Vercel token", "unexpectedError": "An unexpected error occurred"}}, "apiKeys": {"title": "API Keys", "subtitle": "Generate and manage API keys for programmatic access", "generate": "Generate API Key (Coming Soon)"}, "webhooks": {"title": "Webhooks", "subtitle": "Configure webhooks to receive real-time updates", "configure": "Configure <PERSON><PERSON><PERSON><PERSON> (Coming Soon)"}}, "notifications": {"title": "Notification Preferences", "subtitle": "Choose how you want to be notified about important updates", "email": {"title": "Email Notifications", "directoryUpdates": {"label": "Directory Updates", "description": "Get notified about important updates to your directories"}, "newItems": {"label": "New Items", "description": "Get notified when new items are added to your directories"}, "weeklyDigest": {"label": "Weekly Digest", "description": "Receive a weekly summary of your directory activity"}, "marketing": {"label": "Marketing & Promotions", "description": "Receive updates about new features and offers"}}, "app": {"title": "In-App Notifications", "newItems": {"label": "New Items", "description": "Show notifications for new directory items"}, "comments": {"label": "Comments", "description": "Get notified about comments on your directories"}, "mentions": {"label": "Mentions", "description": "Get notified when someone mentions you"}, "systemUpdates": {"label": "System Updates", "description": "Important system notifications and maintenance alerts"}}, "actions": {"save": "Save Preferences", "saving": "Saving..."}, "messages": {"success": "Notification preferences updated", "error": "Failed to update preferences", "unexpectedError": "An unexpected error occurred"}}, "sidebar": {"menu": "<PERSON><PERSON>", "aiChat": "AI Chat", "newDirectory": "New Directory", "navigation": {"dashboard": "Dashboard", "directories": "Directories", "settings": "Settings"}, "signOut": "Sign Out", "signingOut": "Signing out..."}, "dangerZone": {"title": "Danger Zone", "subtitle": "Irreversible and destructive actions", "export": {"title": "Export Your Data", "subtitle": "Download all your directories, items, and settings", "action": "Export Data (Coming Soon)"}, "delete": {"title": "Delete Account", "subtitle": "Permanently delete your account and all associated data. This action cannot be undone.", "button": "Delete My Account", "confirmTitle": "⚠️ This will permanently delete:", "confirmItems": ["Your account and profile", "All your directories", "All directory items and data", "API keys and integrations", "All associated websites"], "confirmLabel": "Type your email {email} to confirm", "confirmPlaceholder": "Enter your email", "confirmButton": "Yes, Delete My Account", "deleting": "Deleting...", "cancel": "Cancel"}, "messages": {"confirmEmail": "Please enter your email correctly to confirm", "deleteSuccess": "Account deleted successfully", "deleteFailed": "Failed to delete account", "unexpectedError": "An unexpected error occurred"}}}, "layout": {"auth": {"backToHome": "Back to home", "feature": {"title": "Build Directories with AI", "subtitle": "Create beautiful, searchable directories in minutes using natural language. No coding required.", "benefits": {"ai": {"title": "AI-Powered Creation", "description": "Describe what you want and let AI do the work"}, "templates": {"title": "Professional Templates", "description": "Start with beautiful, customizable designs"}, "management": {"title": "Easy Management", "description": "Update and organize your content effortlessly"}}}}}, "errors": {"global": {"title": "Something went wrong!", "tryAgain": "Try again"}}, "metadata": {"title": "<PERSON> - Directory Builder", "description": "A SaaS platform for building and managing directories"}, "api": {"errors": {"unauthorized": "You are not authorized to perform this action", "unauthorizedLogin": "Unauthorized: Please log in again.", "forbidden": "Forbidden: You do not have permission to perform this action.", "serverError": "Server error: {message}", "unexpected": "Unexpected server error occurred.", "apiError": "API Error: {status} {statusText}", "loginFailed": "<PERSON><PERSON> failed", "registerFailed": "Failed to register", "unsupportedProvider": "Unsupported provider", "providerConnectFailed": "Failed to connect provider", "forgotPasswordFailed": "Failed to send password reset email", "resetPasswordFailed": "Failed to reset password"}}, "actions": {"directories": {"slug": {"required": "Slug is required", "format": "Slug must be lowercase letters, numbers, and hyphens only"}, "name": {"required": "Name is required", "maxLength": "Name must be less than 100 characters"}, "description": {"required": "Description is required", "maxLength": "Description must be less than 500 characters"}, "prompt": {"minLength": "Prompt must be at least 10 characters", "maxLength": "Prompt must be less than 1000 characters"}, "githubRequired": "GitHub connection required. Please connect your GitHub account first.", "createSuccess": "Directory created successfully!", "createFailed": "Failed to create directory", "invalidGeneratedData": "Failed to generate valid directory data", "aiGenerationStarted": "Directory creation started! AI is generating content...", "invalidId": "Invalid directory ID", "deleteSuccess": "Directory deleted successfully", "deleteFailed": "Failed to delete directory", "fetchFailed": "Failed to fetch directories"}, "settings": {"profile": {"usernameRequired": "Username is required", "usernameMaxLength": "Username must be less than 50 characters", "usernameFormat": "Username can only contain letters, numbers, dashes and underscores", "updateFailed": "Failed to update profile", "notAuthenticated": "Not authenticated"}, "password": {"currentRequired": "Current password is required", "newMinLength": "Password must be at least 8 characters", "newMaxLength": "Password must be less than 100 characters", "updateFailed": "Failed to update password", "updateSuccess": "Password updated successfully", "notAuthenticated": "Not authenticated"}, "vercel": {"notAuthenticated": "Not authenticated", "tokenRequired": "Token is required", "invalidFormat": "Invalid Vercel token format", "saveFailed": "Failed to save <PERSON><PERSON><PERSON> token", "saveSuccess": "Vercel token saved successfully", "removeFailed": "Failed to remove Vercel token", "removeSuccess": "Vercel token removed successfully"}, "github": {"notAuthenticated": "Not authenticated", "disconnectFailed": "Failed to disconnect GitHub", "disconnectSuccess": "GitHub account disconnected successfully"}, "notifications": {"notAuthenticated": "Not authenticated", "updateFailed": "Failed to update preferences", "updateSuccess": "Notification preferences updated successfully"}, "danger": {"notAuthenticated": "Not authenticated", "deleteFailed": "Failed to delete account", "deleteDisabled": "Account deletion is disabled in demo"}}}, "validation": {"auth": {"invalidCredentials": "Invalid email or password", "account": {"suspended": "Account is suspended"}, "email": {"required": "Email cannot be empty", "invalid": "Invalid email", "emailAlreadyRegistered": "Email is already registered"}, "password": {"required": "Password cannot be empty", "minLength": "Password must be at least {length} characters", "lowercase": "Password must contain at least one lowercase letter", "numberOrSpecial": "Password must contain at least one number or special character", "cannotStartWith": "Password cannot start with a dot or newline", "digit": "Password must contain at least one digit"}, "username": {"minLength": "Username must contain at least {length} characters"}}}}